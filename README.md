# 🕌 مكتبة الإعجاز العلمي المطورة

## 📖 نظرة عامة

مكتبة الإعجاز العلمي المطورة هي أداة شاملة ومتقدمة تجمع مئات الآيات القرآنية والأحاديث النبوية مع التحليل العلمي المتقدم والربط بالاكتشافات الحديثة. تستخدم التطبيق تقنيات الذكاء الصناعي المتطورة لتوفير بحث شامل ومفصل.

## ✨ الميزات الرئيسية

### 🤖 البحث بالذكاء الصناعي
- **تكامل Gemini API**: استخدام 15 مفتاح API مع نظام تدوير ذكي
- **بحث شامل**: العثور على مئات الآيات والأحاديث ذات الصلة
- **تحليل متقدم**: ربط النصوص بالاكتشافات العلمية الحديثة

### 📊 مستويات البحث المتعددة
- **أساسي**: 25 آية + 15 حديث مع تحليل سريع
- **شامل**: 60 آية + 40 حديث مع تحليل متقدم
- **مكثف**: 100+ آية + 60+ حديث مع تحليل شامل

### 🎨 واجهة مستخدم متطورة
- **تصميم عربي**: واجهة مصممة خصيصاً للمحتوى العربي
- **تفاعلية**: عناصر تفاعلية لتحسين تجربة المستخدم
- **استجابة**: تصميم متجاوب يعمل على جميع الأجهزة

### 📈 تحليل مفصل
- **نسب التوافق**: تحديد دقة التوافق مع العلم الحديث
- **تبرير مفصل**: شرح أسباب كل نسبة توافق
- **مصادر موثقة**: مراجع علمية قابلة للتحقق

## 🚀 كيفية الاستخدام

### التشغيل المحلي
```bash
# تشغيل خادم محلي
python -m http.server 8000

# فتح المتصفح على
http://localhost:8000
```

### البحث
1. أدخل المجال العلمي المطلوب (مثل: علم الأجنة، الفلك، الجيولوجيا)
2. اختر مستوى عمق البحث
3. حدد أنواع المصادر المطلوبة
4. انقر على "بحث شامل"

## 🔧 التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3**: التصميم والتنسيق
- **JavaScript ES6+**: المنطق والتفاعل
- **Gemini AI API**: الذكاء الصناعي للبحث
- **Font Awesome**: الأيقونات
- **Google Fonts**: الخطوط العربية

## 📚 المجالات العلمية المدعومة

- علم الأجنة والتطور
- علم الفلك والكونيات
- الجيولوجيا وعلوم الأرض
- الطب والصحة
- الفيزياء والكيمياء
- علوم البحار والمحيطات
- علم النفس والسلوك
- الرياضيات والهندسة

## 🎯 الميزات المتقدمة

### تحليل الاعتراضات
- عرض الاعتراضات الشائعة
- ردود مفصلة بالأدلة العلمية
- مصادر موثقة للتحقق

### الإحصائيات العلمية
- أرقام دقيقة قابلة للتحقق
- مراجع علمية محددة
- روابط للدراسات الأصلية

### التصدير المتقدم
- HTML تفاعلي
- PDF احترافي
- Excel منظم
- JSON للمطورين

## 🔒 الأمان والخصوصية

- لا يتم حفظ بيانات المستخدمين
- استخدام آمن لـ APIs
- تشفير الاتصالات

## 📞 الدعم والمساهمة

هذا المشروع مفتوح المصدر ونرحب بالمساهمات والتحسينات.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**تم تطوير هذا المشروع بعناية فائقة لخدمة الباحثين والمهتمين بالإعجاز العلمي في القرآن والسنة**
