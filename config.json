{"app": {"name": "مكتبة الإعجاز العلمي المطورة", "version": "2.0.0", "description": "أداة شاملة لاكتشاف الإعجاز العلمي في القرآن والسنة", "author": "فريق التطوير", "language": "ar", "direction": "rtl"}, "search": {"maxResults": {"basic": {"verses": 25, "hadiths": 15}, "comprehensive": {"verses": 60, "hadiths": 40}, "exhaustive": {"verses": 100, "hadiths": 60}}, "defaultDepth": "comprehensive", "supportedTopics": ["علم الأجنة", "الفلك", "الجيولوجيا", "الطب", "الفيزياء", "الكيمياء", "علوم البحار", "علم النفس", "الرياضيات", "الهندسة"]}, "ai": {"provider": "gemini", "model": "gemini-2.0-flash", "maxRetries": 3, "timeout": 30000, "keyRotation": true, "fallbackToLocal": true}, "ui": {"theme": "light", "forceLightMode": true, "animations": true, "rtlSupport": true, "responsiveBreakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1200px"}}, "export": {"supportedFormats": ["html", "pdf", "excel", "json"], "defaultFormat": "html", "includeMetadata": true, "compression": false}, "features": {"aiSearch": true, "localFallback": true, "advancedAnalysis": true, "objectionRefutation": true, "statisticalData": true, "sourceVerification": true, "exportFunctionality": true, "progressTracking": true}}