:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* تعطيل Dark Mode - Light Theme فقط */
@media (prefers-color-scheme: dark) {
  :root {
    /* فرض Light Theme حتى في Dark Mode */
    --color-background: var(--color-cream-50);
    --color-surface: var(--color-cream-100);
    --color-text: var(--color-slate-900);
    --color-text-secondary: var(--color-slate-500);
    --color-primary: var(--color-teal-500);
    --color-primary-hover: var(--color-teal-600);
    --color-primary-active: var(--color-teal-700);
    --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
    --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
    --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
    --color-border: rgba(var(--color-brown-600-rgb), 0.2);
    --color-btn-primary-text: var(--color-cream-50);
    --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
    --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
    --color-error: var(--color-red-500);
    --color-success: var(--color-teal-500);
    --color-warning: var(--color-orange-500);
    --color-info: var(--color-slate-500);
    --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
    --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

    /* RGB versions for light mode */
    --color-success-rgb: var(--color-teal-500-rgb);
    --color-error-rgb: var(--color-red-500-rgb);
    --color-warning-rgb: var(--color-orange-500-rgb);
    --color-info-rgb: var(--color-slate-500-rgb);
  }
}

/* فرض Light Theme دائماً */
[data-color-scheme="dark"] {
  /* فرض Light Theme حتى مع data attribute */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* تصميم مكتبة الإعجاز العلمي المطورة - Light Theme */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

/* متغيرات التصميم Light */
:root {
  /* ألوان أساسية فاتحة */
  --color-primary: #22C55E;
  --color-primary-light: #BBF7D0;
  --color-secondary: #F59E0B;
  --color-secondary-light: #FEF3C7;
  --color-accent: #3B82F6;
  --color-accent-light: #DBEAFE;
  
  /* ألوان الخلفية */
  --color-bg-main: #FFFFFF;
  --color-bg-secondary: #F8FAFC;
  --color-bg-card: #FFFFFF;
  --color-bg-light: #F0F9FF;
  --color-bg-success: #ECFDF5;
  --color-bg-warning: #FFFBEB;
  --color-bg-info: #EFF6FF;
  
  /* ألوان النصوص */
  --color-text-primary: #1F2937;
  --color-text-secondary: #6B7280;
  --color-text-muted: #9CA3AF;
  --color-text-white: #FFFFFF;
  
  /* ألوان الحدود */
  --color-border-light: #E5E7EB;
  --color-border-primary: #22C55E;
  --color-border-secondary: #F59E0B;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* خصائص أخرى */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  --font-cairo: 'Cairo', sans-serif;
  --font-tajawal: 'Tajawal', sans-serif;
  
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* إعادة تعيين أساسية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-cairo);
  direction: rtl;
  text-align: right;
  background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 50%, #F0FDF4 100%);
  color: var(--color-text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header */
.main-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
  color: var(--color-text-white);
  padding: 3rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.main-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm15 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-icon {
  font-size: 4rem;
  color: var(--color-secondary);
  margin-bottom: 1rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.main-title {
  font-family: var(--font-tajawal);
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 1.25rem;
  opacity: 0.95;
  font-weight: 300;
  max-width: 600px;
  margin: 0 auto;
}

/* Main Content */
.main-content {
  padding: 4rem 0;
}

/* Search Section */
.search-section {
  margin-bottom: 3rem;
}

.search-card {
  background: var(--color-bg-card);
  border-radius: var(--radius-xl);
  padding: 3rem;
  box-shadow: var(--shadow-lg);
  border: 2px solid var(--color-primary-light);
  position: relative;
  overflow: hidden;
}

.search-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-secondary), var(--color-accent));
  border-radius: var(--radius-xl);
  z-index: -1;
  opacity: 0.1;
}

.search-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.search-header i {
  font-size: 3rem;
  color: var(--color-primary);
  margin-bottom: 1rem;
}

.search-header h2 {
  font-family: var(--font-tajawal);
  font-size: 2rem;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.search-header p {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
  max-width: 700px;
  margin: 0 auto;
}

/* Search Input */
.search-input-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: stretch;
}

.search-input {
  flex: 1;
  padding: 1.25rem 1.5rem;
  border: 2px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  font-size: 1.125rem;
  font-family: var(--font-cairo);
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  background: var(--color-bg-main);
  box-shadow: 0 0 0 4px var(--color-primary-light);
}

.search-btn {
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: var(--color-text-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1.125rem;
  font-weight: 600;
  font-family: var(--font-tajawal);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-md);
  white-space: nowrap;
}

.search-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, #16A34A, #2563EB);
}

.search-btn i {
  margin-left: 0.5rem;
}

/* Search Options */
.search-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 2rem;
  background: var(--color-bg-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border-light);
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-label {
  font-weight: 600;
  font-family: var(--font-tajawal);
  color: var(--color-text-primary);
  font-size: 1.125rem;
}

.radio-group, .checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-option, .checkbox-option {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.radio-option:hover, .checkbox-option:hover {
  background: var(--color-bg-secondary);
}

.radio-custom, .checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border-light);
  border-radius: 50%;
  position: relative;
  margin-top: 2px;
  flex-shrink: 0;
  transition: var(--transition);
}

.checkbox-custom {
  border-radius: var(--radius-sm);
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: var(--color-primary);
  background: var(--color-primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom {
  border-color: var(--color-primary);
  background: var(--color-primary);
}

.checkbox-option input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.radio-option input, .checkbox-option input {
  display: none;
}

.radio-option small, .checkbox-option small {
  display: block;
  color: var(--color-text-muted);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Progress Section */
.progress-section {
  margin-bottom: 3rem;
  animation: fadeIn 0.5s ease-out;
}

.progress-card {
  background: var(--color-bg-card);
  border-radius: var(--radius-xl);
  padding: 2.5rem;
  box-shadow: var(--shadow-lg);
  border: 2px solid var(--color-accent-light);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.progress-header h3 {
  font-family: var(--font-tajawal);
  font-size: 1.5rem;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.progress-controls {
  display: flex;
  gap: 0.5rem;
}

.progress-bar-container {
  width: 100%;
  height: 12px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: 1rem;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: var(--color-bg-light);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-light);
  text-align: center;
}

.stat-item i {
  font-size: 1.5rem;
  color: var(--color-primary);
  margin-bottom: 0.5rem;
}

.stat-item span:nth-child(2) {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.stat-item span:last-child {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-text-primary);
}

/* Results Overview */
.results-overview {
  margin-bottom: 3rem;
}

.overview-card {
  background: var(--color-bg-card);
  border-radius: var(--radius-xl);
  padding: 2.5rem;
  box-shadow: var(--shadow-lg);
  border: 2px solid var(--color-secondary-light);
}

.overview-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.overview-header h2 {
  font-family: var(--font-tajawal);
  font-size: 2rem;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: var(--color-bg-main);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 2px solid transparent;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.verse-stat {
  border-color: var(--color-primary-light);
}

.verse-stat .stat-icon {
  color: var(--color-primary);
}

.hadith-stat {
  border-color: var(--color-secondary-light);
}

.hadith-stat .stat-icon {
  color: var(--color-secondary);
}

.miracle-stat {
  border-color: var(--color-accent-light);
}

.miracle-stat .stat-icon {
  color: var(--color-accent);
}

.accuracy-stat {
  border-color: var(--color-primary-light);
}

.accuracy-stat .stat-icon {
  color: var(--color-primary);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.stat-content h3 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
}

.stat-content p {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.stat-content small {
  color: var(--color-text-muted);
  font-size: 0.875rem;
}

/* Interactive Results */
.interactive-results {
  margin-bottom: 3rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.results-header h2 {
  font-family: var(--font-tajawal);
  font-size: 1.75rem;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.results-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-filter {
  position: relative;
}

.filter-input {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-md);
  font-family: var(--font-cairo);
  background: var(--color-bg-main);
  width: 250px;
}

.search-filter i {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-muted);
}

.sort-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-md);
  font-family: var(--font-cairo);
  background: var(--color-bg-main);
}

/* Interactive Cards */
.interactive-cards {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.interactive-card {
  background: var(--color-bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 2px solid var(--color-border-light);
  transition: var(--transition);
}

.interactive-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.card-header {
  background: linear-gradient(135deg, var(--color-bg-light), var(--color-bg-secondary));
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-light);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-info {
  flex: 1;
}

.source-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.info-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
}

.surah-badge {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.verse-badge {
  background: var(--color-secondary-light);
  color: var(--color-secondary);
}

.revelation-badge {
  background: var(--color-accent-light);
  color: var(--color-accent);
}

.hadith-badge {
  background: var(--color-bg-warning);
  color: var(--color-secondary);
}

.authenticity-badge {
  background: var(--color-bg-success);
  color: var(--color-primary);
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.card-text {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-light);
}

.arabic-text {
  font-family: var(--font-tajawal);
  font-size: 1.25rem;
  line-height: 2;
  color: var(--color-text-primary);
  font-weight: 500;
  text-align: justify;
  margin-bottom: 1rem;
}

.card-expandable {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.card-expandable.expanded {
  max-height: 2000px;
}

.miracle-content {
  padding: 1.5rem;
  background: var(--color-bg-success);
  border-bottom: 1px solid var(--color-border-light);
}

.scientific-content {
  padding: 1.5rem;
  background: var(--color-bg-info);
  border-bottom: 1px solid var(--color-border-light);
}

.accuracy-content {
  padding: 1.5rem;
  background: var(--color-bg-warning);
  border-bottom: 1px solid var(--color-border-light);
}

.objections-content {
  padding: 1.5rem;
  background: var(--color-bg-secondary);
}

.content-section {
  margin-bottom: 1.5rem;
}

.content-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-family: var(--font-tajawal);
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-text {
  color: var(--color-text-secondary);
  line-height: 1.7;
}

.accuracy-bar {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  height: 8px;
  margin: 0.5rem 0;
}

.accuracy-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  border-radius: var(--radius-sm);
  transition: width 0.5s ease;
}

/* تحسينات جديدة لعرض التفاصيل */
.verse-details, .hadith-details {
  background: var(--color-bg-light);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1rem 0;
  border: 1px solid var(--color-border-light);
}

.verse-number {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--color-primary-light);
  color: var(--color-primary);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  margin-bottom: 1rem;
}

.hadith-authenticity {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  margin-bottom: 1rem;
}

.hadith-authenticity.صحيح {
  background: var(--color-bg-success);
  color: var(--color-primary);
}

.hadith-authenticity.حسن {
  background: var(--color-bg-warning);
  color: var(--color-secondary);
}

.hadith-authenticity.ضعيف {
  background: #FEE2E2;
  color: #DC2626;
}

.miracle-aspects {
  background: var(--color-bg-success);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1rem 0;
}

.aspect-item {
  background: var(--color-bg-main);
  border-radius: var(--radius-sm);
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--color-primary-light);
}

.aspect-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.aspect-title {
  font-weight: 600;
  color: var(--color-text-primary);
}

.aspect-strength {
  background: var(--color-primary);
  color: var(--color-text-white);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 600;
}

.aspect-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.scientific-basis {
  background: var(--color-bg-info);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1rem 0;
}

.evidence-list, .sources-list {
  margin-top: 1rem;
}

.evidence-list h6, .sources-list h6 {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.evidence-list ul, .sources-list ul {
  list-style: none;
  padding: 0;
}

.evidence-list li, .sources-list li {
  background: var(--color-bg-main);
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: var(--radius-sm);
  border-left: 4px solid var(--color-accent);
  color: var(--color-text-secondary);
}

.objections-refutations {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1rem 0;
}

.objection-item {
  background: var(--color-bg-main);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
}

.objection {
  background: #FEF3C7;
  border-left: 4px solid var(--color-secondary);
  padding: 1rem;
  border-radius: var(--radius-sm);
  margin-bottom: 1rem;
}

.objection h6 {
  color: var(--color-secondary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refutation {
  background: var(--color-bg-success);
  border-left: 4px solid var(--color-primary);
  padding: 1rem;
  border-radius: var(--radius-sm);
}

.refutation h6 {
  color: var(--color-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.accuracy-breakdown {
  background: var(--color-bg-warning);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  margin: 1rem 0;
}

.accuracy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--color-bg-main);
  border-radius: var(--radius-sm);
  margin-bottom: 0.5rem;
  border: 1px solid var(--color-border-light);
}

.accuracy-label {
  font-weight: 500;
  color: var(--color-text-primary);
}

.accuracy-value {
  font-weight: 600;
  color: var(--color-primary);
}

.accuracy-reason {
  background: var(--color-bg-light);
  padding: 1rem;
  border-radius: var(--radius-sm);
  margin-top: 1rem;
  border-left: 4px solid var(--color-primary);
  font-style: italic;
  color: var(--color-text-secondary);
}

/* أنماط جديدة للتحسينات */
.ai-badge {
  background: linear-gradient(135deg, #8B5CF6, #3B82F6);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.5rem;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--color-bg-main);
  border-radius: var(--radius-sm);
  border: 1px solid var(--color-border-light);
}

.detail-label {
  font-weight: 600;
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-value {
  font-weight: 500;
  color: var(--color-text-primary);
  font-size: 1rem;
}

.authenticity-صحيح {
  color: var(--color-primary) !important;
  font-weight: 600;
}

.authenticity-حسن {
  color: var(--color-secondary) !important;
  font-weight: 600;
}

.authenticity-ضعيف {
  color: #DC2626 !important;
  font-weight: 600;
}

.authenticity-explanation {
  background: var(--color-bg-light);
  padding: 1rem;
  border-radius: var(--radius-sm);
  margin-top: 1rem;
  border-left: 4px solid var(--color-primary);
}

.authenticity-desc {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
}

.statistics-list, .links-list {
  margin-top: 1rem;
}

.statistics-list h6, .links-list h6 {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.statistics-list ul, .links-list ul {
  list-style: none;
  padding: 0;
}

.statistics-list li, .links-list li {
  background: var(--color-bg-main);
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: var(--radius-sm);
  border-left: 4px solid var(--color-accent);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.links-list a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.links-list a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

.objection-meta, .refutation-meta {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.severity-badge, .frequency-badge, .strength-badge, .evidence-badge {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.severity-متوسط {
  background: var(--color-bg-warning);
  color: var(--color-secondary);
}

.severity-عالي {
  background: #FEE2E2;
  color: #DC2626;
}

.severity-منخفض {
  background: var(--color-bg-success);
  color: var(--color-primary);
}

.frequency-badge {
  background: var(--color-bg-info);
  color: var(--color-accent);
}

.strength-قوي, .strength-قوي-جداً {
  background: var(--color-bg-success);
  color: var(--color-primary);
}

.strength-متوسط {
  background: var(--color-bg-warning);
  color: var(--color-secondary);
}

.evidence-badge {
  background: var(--color-bg-light);
  color: var(--color-text-primary);
}

.refutation-sources {
  margin-top: 1rem;
  background: var(--color-bg-light);
  padding: 1rem;
  border-radius: var(--radius-sm);
}

.refutation-sources h6 {
  color: var(--color-text-primary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refutation-sources ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.refutation-sources li {
  background: var(--color-bg-main);
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: var(--radius-sm);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-family: var(--font-cairo);
}

.btn-primary {
  background: var(--color-primary);
  color: var(--color-text-white);
}

.btn-primary:hover {
  background: #16A34A;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-text-white);
}

.btn-secondary:hover {
  background: #D97706;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn-outline:hover {
  background: var(--color-primary);
  color: var(--color-text-white);
}

.btn-success {
  background: var(--color-primary);
  color: var(--color-text-white);
}

.btn-warning {
  background: var(--color-secondary);
  color: var(--color-text-white);
}

.btn-danger {
  background: #EF4444;
  color: var(--color-text-white);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--color-bg-card);
  border-radius: var(--radius-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  z-index: 1001;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-family: var(--font-tajawal);
  font-size: 1.5rem;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.modal-close:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.modal-body {
  padding: 1.5rem;
}

.export-formats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.export-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  border: 2px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  background: var(--color-bg-main);
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  color: var(--color-text-primary);
}

.export-btn:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.export-btn i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-primary);
}

.export-btn span {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.export-btn small {
  color: var(--color-text-muted);
  font-size: 0.875rem;
}

/* Footer */
.main-footer {
  background: linear-gradient(135deg, var(--color-text-primary), #374151);
  color: var(--color-text-white);
  padding: 3rem 0 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

.footer-info h3 {
  font-family: var(--font-tajawal);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-info p {
  color: #D1D5DB;
  line-height: 1.7;
  max-width: 500px;
}

.footer-features h4 {
  font-family: var(--font-tajawal);
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--color-secondary);
}

.feature-list ul {
  list-style: none;
  padding: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #D1D5DB;
}

.feature-list i {
  color: var(--color-primary);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: 1rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .search-card {
    padding: 2rem 1.5rem;
  }
  
  .search-input-container {
    flex-direction: column;
  }
  
  .search-options {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .results-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .results-controls {
    flex-direction: column;
  }
  
  .progress-stats {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
  
  .filter-input {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .main-header {
    padding: 2rem 0;
  }
  
  .search-card {
    border-radius: var(--radius-lg);
  }
  
  .overview-stats {
    grid-template-columns: 1fr;
  }
  
  .export-formats {
    grid-template-columns: 1fr;
  }
  
  .stat-content h3 {
    font-size: 2rem;
  }
}